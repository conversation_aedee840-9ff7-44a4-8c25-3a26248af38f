<nav class="fixed w-full z-50 bg-white/90 dark:bg-gray-900/90 backdrop-blur-md border-b border-gray-200 dark:border-gray-800">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between h-16">
      <div class="flex items-center">
        <a (click)="scrollToTop($event)" class="flex-shrink-0 cursor-pointer">
          <img class="h-20 w-auto hidden dark:block" src="/assets/images/white-text-logo.png" alt="Lunatem">
          <img class="h-20 w-auto block dark:hidden" src="/assets/images/text-logo.png" alt="Lunatem">
        </a>
      </div>

      <!-- Desktop menu -->
      <div class="hidden md:flex items-center space-x-8">
        <a href="#services" class="text-gray-700 dark:text-gray-300 hover:text-[#0090EC] dark:hover:text-[#0090EC] px-3 py-2 text-sm font-medium transition-colors relative" [class.active-link]="activeSection === 'services'">
          Services
          <span class="absolute bottom-0 left-0 w-full h-0.5 bg-[#0090EC] transform scale-x-0 transition-transform origin-left" [class.scale-x-100]="activeSection === 'services'"></span>
        </a>
        <a href="#projects" class="text-gray-700 dark:text-gray-300 hover:text-[#0090EC] dark:hover:text-[#0090EC] px-3 py-2 text-sm font-medium transition-colors relative" [class.active-link]="activeSection === 'projects'">
          Réalisations
          <span class="absolute bottom-0 left-0 w-full h-0.5 bg-[#0090EC] transform scale-x-0 transition-transform origin-left" [class.scale-x-100]="activeSection === 'projects'"></span>
        </a>
        <a href="#contact" class="text-gray-700 dark:text-gray-300 hover:text-[#0090EC] dark:hover:text-[#0090EC] px-3 py-2 text-sm font-medium transition-colors relative" [class.active-link]="activeSection === 'contact'">
          Contact
          <span class="absolute bottom-0 left-0 w-full h-0.5 bg-[#0090EC] transform scale-x-0 transition-transform origin-left" [class.scale-x-100]="activeSection === 'contact'"></span>
        </a>
        <button (click)="toggleDarkMode()" class="p-2 rounded-full text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 focus:outline-none">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 hidden dark:block" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
          </svg>
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 block dark:hidden" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
          </svg>
        </button>
      </div>

      <!-- Mobile menu button -->
      <div class="md:hidden flex items-center">
        <button (click)="toggleDarkMode()" class="p-2 mr-2 rounded-full text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 focus:outline-none">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 hidden dark:block" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
          </svg>
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 block dark:hidden" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
          </svg>
        </button>
        <button (click)="toggleMenu()" class="p-2 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 focus:outline-none">
          <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        </button>
      </div>
    </div>
  </div>

  <!-- Mobile menu -->
  @if (isMenuOpen) {
    <div class="md:hidden bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800">
      <div class="px-2 pt-2 pb-3 space-y-1">
        <a href="#services" class="block px-3 py-2 rounded-md text-base font-medium relative"
           [class.text-[#0090EC]]="activeSection === 'services'"
           [class.text-gray-700]="activeSection !== 'services'"
           [class.dark:text-[#0090EC]]="activeSection === 'services'"
           [class.dark:text-gray-300]="activeSection !== 'services'">
          Services
          <span class="absolute left-0 w-1 h-full bg-[#0090EC] rounded-r transform scale-y-0 transition-transform origin-bottom" [class.scale-y-100]="activeSection === 'services'"></span>
        </a>
        <a href="#projects" class="block px-3 py-2 rounded-md text-base font-medium relative"
           [class.text-[#0090EC]]="activeSection === 'projects'"
           [class.text-gray-700]="activeSection !== 'projects'"
           [class.dark:text-[#0090EC]]="activeSection === 'projects'"
           [class.dark:text-gray-300]="activeSection !== 'projects'">
          Réalisations
          <span class="absolute left-0 w-1 h-full bg-[#0090EC] rounded-r transform scale-y-0 transition-transform origin-bottom" [class.scale-y-100]="activeSection === 'projects'"></span>
        </a>
        <a href="#contact" class="block px-3 py-2 rounded-md text-base font-medium relative"
           [class.text-[#0090EC]]="activeSection === 'contact'"
           [class.text-gray-700]="activeSection !== 'contact'"
           [class.dark:text-[#0090EC]]="activeSection === 'contact'"
           [class.dark:text-gray-300]="activeSection !== 'contact'">
          Contact
          <span class="absolute left-0 w-1 h-full bg-[#0090EC] rounded-r transform scale-y-0 transition-transform origin-bottom" [class.scale-y-100]="activeSection === 'contact'"></span>
        </a>
      </div>
    </div>
  }
</nav>
