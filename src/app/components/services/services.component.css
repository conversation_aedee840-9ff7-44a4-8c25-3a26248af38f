
.service-card {
  border: 1px solid rgba(0, 144, 236, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  border-radius: 0.75rem;
}

.service-card::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: inherit;
  padding: 1px;
  background: linear-gradient(to right, transparent, rgba(0, 144, 236, 0.2), transparent);
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  opacity: 0.3; /* Visible même sans survol */
  transition: opacity 0.4s ease;
}

.service-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px -10px rgba(0, 0, 0, 0.1);
  border-color: rgba(0, 144, 236, 0.3);
}

.service-card:hover::before {
  opacity: 1;
}

.dark .service-card {
  border-color: rgba(0, 144, 236, 0.15);
}

.dark .service-card::before {
  background: linear-gradient(to right, transparent, rgba(0, 144, 236, 0.3), transparent);
}

.dark .service-card:hover {
  box-shadow: 0 20px 40px -10px rgba(0, 0, 0, 0.4);
  border-color: rgba(0, 144, 236, 0.4);
}
