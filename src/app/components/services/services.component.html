<section id="services" class="py-16 bg-gray-50 dark:bg-gray-800">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center">
      <h2 class="text-3xl md:text-4xl font-bold text-[#040F1A] dark:text-white">Nos Services</h2>
      <p class="mt-4 max-w-2xl mx-auto text-gray-600 dark:text-gray-300">
        Des solutions digitales sur mesure pour répondre à tous vos besoins
      </p>
    </div>

    <div class="mt-16 grid gap-8 md:grid-cols-2 lg:grid-cols-4">
      @for (service of services; track service.title) {
        <div class="bg-white dark:bg-gray-900 rounded-xl shadow-lg p-6 transition-transform hover:scale-105 service-card">
          <div class="w-12 h-12 rounded-full bg-[#0090EC]/10 flex items-center justify-center mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-[#0090EC]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" [attr.d]="service.icon" />
            </svg>
          </div>
          <h3 class="text-xl font-semibold text-[#040F1A] dark:text-white">{{ service.title }}</h3>
          <p class="mt-2 text-gray-600 dark:text-gray-300">{{ service.description }}</p>
        </div>
      }
    </div>
  </div>
</section>
