<section id="contact" class="py-16">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center">
      <h2 class="text-3xl md:text-4xl font-bold text-[#040F1A] dark:text-white">Contactez-nous</h2>
      <p class="mt-4 max-w-2xl mx-auto text-gray-600 dark:text-gray-300">
        Discutons de votre projet et voyons comment nous pouvons vous aider
      </p>
    </div>

    <div class="mt-12 grid gap-8 md:grid-cols-2">
      <!-- Contact Form -->
      <div class="bg-white/90 dark:bg-gray-900/90 backdrop-blur-xl rounded-3xl shadow-2xl p-8 border border-gray-100/50 dark:border-gray-700/30 hover:shadow-[0_20px_50px_rgba(0,144,236,0.1)] transition-all duration-300">
        <h3 class="text-2xl font-semibold text-[#040F1A] dark:text-white mb-6">Envoyez-nous un message</h3>
        <form (ngSubmit)="submitForm()">
          <div class="space-y-6">
            <div>
              <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Nom <span class="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="name"
                name="name"
                [(ngModel)]="contactForm.name"
                #nameInput="ngModel"
                required
                class="w-full px-4 py-3 rounded-xl bg-gray-50/50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 focus:outline-none focus:ring-2 focus:ring-[#0090EC] focus:border-transparent transition-all duration-200"
                [ngClass]="{'border-red-500': nameInput.invalid && (nameInput.dirty || nameInput.touched || formSubmitted)}"
                placeholder="Votre nom"
              >
              <div *ngIf="nameInput.invalid && (nameInput.dirty || nameInput.touched || formSubmitted)" class="text-red-500 text-sm mt-1">
                Ce champ est obligatoire
              </div>
            </div>
            <div>
              <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Email <span class="text-red-500">*</span>
              </label>
              <input
                type="email"
                id="email"
                name="email"
                [(ngModel)]="contactForm.email"
                #emailInput="ngModel"
                required
                pattern="^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
                class="w-full px-4 py-3 rounded-xl bg-gray-50/50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 focus:outline-none focus:ring-2 focus:ring-[#0090EC] focus:border-transparent transition-all duration-200"
                [ngClass]="{'border-red-500': emailInput.invalid && (emailInput.dirty || emailInput.touched || formSubmitted)}"
                placeholder="<EMAIL>"
              >
              <div *ngIf="emailInput.invalid && (emailInput.dirty || emailInput.touched || formSubmitted)" class="text-red-500 text-sm mt-1">
                <div *ngIf="emailInput.errors?.['required']">Ce champ est obligatoire</div>
                <div *ngIf="emailInput.errors?.['pattern']">Veuillez entrer une adresse email valide</div>
              </div>
            </div>
            <div>
              <label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Téléphone <span class="text-red-500">*</span>
              </label>
              <input
                type="tel"
                id="phone"
                name="phone"
                [(ngModel)]="contactForm.phone"
                #phoneInput="ngModel"
                required
                class="w-full px-4 py-3 rounded-xl bg-gray-50/50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 focus:outline-none focus:ring-2 focus:ring-[#0090EC] focus:border-transparent transition-all duration-200"
                [ngClass]="{'border-red-500': phoneInput.invalid && (phoneInput.dirty || phoneInput.touched || formSubmitted)}"
                placeholder="+212 6XX XXX XXX"
              >
              <div *ngIf="phoneInput.invalid && (phoneInput.dirty || phoneInput.touched || formSubmitted)" class="text-red-500 text-sm mt-1">
                Ce champ est obligatoire
              </div>
            </div>
            <div>
              <label for="service" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Service <span class="text-red-500">*</span>
              </label>
              <select
                id="service"
                name="service"
                [(ngModel)]="contactForm.service"
                #serviceInput="ngModel"
                required
                class="w-full px-4 py-3 rounded-xl bg-gray-50/50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 focus:outline-none focus:ring-2 focus:ring-[#0090EC] focus:border-transparent transition-all duration-200 appearance-none"
                [ngClass]="{'border-red-500': serviceInput.invalid && (serviceInput.dirty || serviceInput.touched || formSubmitted)}"
                style="background-image: url('data:image/svg+xml,%3Csvg xmlns=%27http://www.w3.org/2000/svg%27 width=%2724%27 height=%2724%27 viewBox=%270 0 24 24%27 fill=%27none%27 stroke=%27currentColor%27 stroke-width=%272%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27%3E%3Cpolyline points=%276 9 12 15 18 9%27/%3E%3C/svg%3E'); background-repeat: no-repeat; background-position: right 1rem center; background-size: 1em;"
              >
                @for (option of serviceOptions; track option.value) {
                  <option [value]="option.value">{{ option.label }}</option>
                }
              </select>
              <div *ngIf="serviceInput.invalid && (serviceInput.dirty || serviceInput.touched || formSubmitted)" class="text-red-500 text-sm mt-1">
                Veuillez sélectionner un service
              </div>
            </div>
            <div>
              <label for="message" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Message
              </label>
              <textarea
                id="message"
                name="message"
                [(ngModel)]="contactForm.message"
                #messageInput="ngModel"
                rows="4"
                class="w-full px-4 py-3 rounded-xl bg-gray-50/50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 focus:outline-none focus:ring-2 focus:ring-[#0090EC] focus:border-transparent transition-all duration-200"
                placeholder="Comment pouvons-nous vous aider ?"
              ></textarea>
            </div>

            <!-- Messages d'état du formulaire -->
            @if (formStatus === 'error') {
              <div class="p-3 bg-red-100 text-red-700 rounded-lg">
                {{ errorMessage }}
              </div>
            }

            @if (formStatus === 'success') {
              <div class="p-3 bg-green-100 text-green-700 rounded-lg">
                Votre message a été envoyé avec succès. Nous vous répondrons dans les plus brefs délais.
              </div>
            }

            <button
              type="submit"
              class="w-full px-6 py-3 rounded-xl bg-[#0090EC] text-white font-medium hover:bg-[#0080d3] transition-all duration-300 shadow-lg hover:shadow-xl hover:shadow-[#0090EC]/20 disabled:opacity-70 disabled:cursor-not-allowed"
              [disabled]="formStatus === 'sending'"
            >
              @if (formStatus === 'sending') {
                <span class="flex items-center justify-center">
                  <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Envoi en cours...
                </span>
              } @else {
                Envoyer
              }
            </button>
          </div>
        </form>
      </div>

      <!-- Contact Info -->
      <div class="bg-gradient-to-br from-white/90 to-gray-50/90 dark:from-gray-900/90 dark:to-gray-800/90 backdrop-blur-xl rounded-3xl shadow-2xl p-8 border border-gray-100/50 dark:border-gray-700/30 flex flex-col justify-center hover:shadow-[0_20px_50px_rgba(0,144,236,0.1)] transition-all duration-300">
        <div class="space-y-8">
          <div class="flex items-start space-x-4">
            <div class="flex-shrink-0 w-12 h-12 rounded-full bg-[#0090EC]/10 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-[#0090EC]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
              </svg>
            </div>
            <div>
              <h3 class="text-xl font-semibold text-[#040F1A] dark:text-white mb-1">Coordonnées</h3>
              <p class="text-gray-600 dark:text-gray-300">
                Service en ligne 100%
              </p>
            </div>
          </div>

          <div class="flex items-start space-x-4">
            <div class="flex-shrink-0 w-12 h-12 rounded-full bg-[#0090EC]/10 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-[#0090EC]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
            </div>
            <div>
              <h3 class="text-xl font-semibold text-[#040F1A] dark:text-white mb-1">Email</h3>
              <a href="mailto:<EMAIL>" class="text-[#0090EC] hover:underline">contact&#64;lunatem.com</a>
            </div>
          </div>

          <div class="flex items-start space-x-4">
            <div class="flex-shrink-0 w-12 h-12 rounded-full bg-[#0090EC]/10 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-[#0090EC]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
              </svg>
            </div>
            <div>
              <h3 class="text-xl font-semibold text-[#040F1A] dark:text-white mb-1">Téléphone</h3>
              <a href="tel:+212781002517" class="text-[#0090EC] hover:underline">+212 781 002 517</a>
            </div>
          </div>

          <div class="flex items-start space-x-4">
            <div class="flex-shrink-0 w-12 h-12 rounded-full bg-[#0090EC]/10 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-[#0090EC]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <div>
              <h3 class="text-xl font-semibold text-[#040F1A] dark:text-white mb-1">Suivez-nous</h3>
              <div class="flex space-x-4 mt-2">
                <a href="https://www.instagram.com/lunatem.digital?utm_source=ig_web_button_share_sheet&igsh=ZDNlZDc0MzIxNw==" target="_blank" class="text-gray-600 dark:text-gray-400 hover:text-[#0090EC] dark:hover:text-[#0090EC] transition-colors">
                  <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path fill-rule="evenodd" d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z" clip-rule="evenodd" />
                  </svg>
                </a>
                <a href="https://www.facebook.com/profile.php?id=61576689530178" target="_blank" class="text-gray-600 dark:text-gray-400 hover:text-[#0090EC] dark:hover:text-[#0090EC] transition-colors">
                  <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path fill-rule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clip-rule="evenodd" />
                  </svg>
                </a>
                <a href="https://www.tiktok.com/@lunatem.digital" target="_blank" class="text-gray-600 dark:text-gray-400 hover:text-[#0090EC] dark:hover:text-[#0090EC] transition-colors">
                  <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"/>
                  </svg>
                </a>
                <a href="https://x.com/Lunatem211536" target="_blank" class="text-gray-600 dark:text-gray-400 hover:text-[#0090EC] dark:hover:text-[#0090EC] transition-colors">
                  <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                  </svg>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
