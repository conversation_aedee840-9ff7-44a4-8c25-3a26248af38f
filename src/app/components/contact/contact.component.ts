import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, NgForm } from '@angular/forms';
import emailjs from '@emailjs/browser';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-contact',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './contact.component.html',
  styleUrl: './contact.component.css'
})
export class ContactComponent implements OnInit {
  @ViewChild('contactForm') ngForm!: NgForm;

  contactForm = {
    name: '',
    email: '',
    phone: '',
    service: '',
    message: ''
  };

  serviceOptions = [
    { value: '', label: 'Sélectionnez un service' },
    { value: 'site-web', label: 'Site Web' },
    { value: 'application-web', label: 'Application Web' },
    { value: 'saas', label: 'SaaS' },
    { value: 'strategie-digitale', label: 'Stratégie Digitale' },
    { value: 'autre', label: 'Autre' }
  ];

  formStatus: 'idle' | 'sending' | 'success' | 'error' = 'idle';
  errorMessage = '';
  formSubmitted = false;

  ngOnInit() {
    // Initialiser EmailJS avec la clé publique depuis l'environnement
    emailjs.init(environment.emailjs.publicKey);
  }

  async submitForm() {
    this.formSubmitted = true;

    if (!this.validateForm()) {
      this.formStatus = 'error';
      this.errorMessage = 'Veuillez remplir correctement tous les champs du formulaire.';
      return;
    }

    this.formStatus = 'sending';

    try {
      // Préparer les paramètres pour EmailJS
      const templateParams = {
        from_name: this.contactForm.name,
        from_email: this.contactForm.email,
        from_phone: this.contactForm.phone,
        service: this.getServiceLabel(this.contactForm.service),
        message: this.contactForm.message || 'Aucun message fourni'
      };

      // Envoyer l'email via EmailJS
      await emailjs.send(
        environment.emailjs.serviceId,
        environment.emailjs.templateId,
        templateParams
      );

      // Réinitialiser le formulaire après envoi réussi
      this.formStatus = 'success';

      // Réinitialiser le formulaire SANS déclencher les validations
      if (this.ngForm) {
        // Désactiver temporairement les validations
        this.formSubmitted = false;

        // Réinitialiser le formulaire
        this.contactForm = { name: '', email: '', phone: '', service: '', message: '' };

        // Réinitialiser l'état du formulaire Angular
        this.ngForm.resetForm();

         // S'assurer que tous les contrôles sont marqués comme non touchés et pristine
        Object.keys(this.ngForm.controls).forEach(key => {
          const control = this.ngForm.controls[key];
          control.markAsPristine();
          control.markAsUntouched();
        });
      }

      // Réinitialiser le statut après 5 secondes
      setTimeout(() => {
        this.formStatus = 'idle';
      }, 5000);
    } catch (error) {
      console.error('Erreur lors de l\'envoi de l\'email:', error);
      this.formStatus = 'error';
      this.errorMessage = 'Une erreur est survenue lors de l\'envoi du message. Veuillez réessayer.';
    }
  }

  validateForm(): boolean {
    return (
      !!this.contactForm.name &&
      !!this.contactForm.email &&
      !!this.contactForm.phone &&
      !!this.contactForm.service &&
      this.validateEmail(this.contactForm.email)
    );
  }

  validateEmail(email: string): boolean {
    const re = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return re.test(email);
  }

  getServiceLabel(value: string): string {
    const option = this.serviceOptions.find(opt => opt.value === value);
    return option ? option.label : value;
  }
}
