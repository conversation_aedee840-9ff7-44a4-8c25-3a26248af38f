<section id="projects" class="py-12 bg-gray-50 dark:bg-gray-800 overflow-hidden">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-8">
      <h2 class="text-3xl md:text-4xl font-bold text-[#040F1A] dark:text-white">Nos Réalisations</h2>
      <p class="mt-2 max-w-2xl mx-auto text-gray-600 dark:text-gray-300">
        Découvrez quelques-uns des projets que nous avons réalisés
      </p>
    </div>

    <div class="relative carousel-wrapper">
      <!-- Navigation buttons -->
      <button (click)="prevProject()" class="absolute left-8 top-1/2 -translate-y-1/2 z-30 bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm rounded-full p-4 shadow-lg hover:bg-white dark:hover:bg-gray-800 focus:outline-none transition-all duration-300">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-[#0090EC]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
        </svg>
      </button>

      <div #carouselContainer class="carousel-container">
        @for (project of projects; track $index) {
          <div class="carousel-item"
               [class.active]="$index === currentIndex"
               [class.prev]="$index === getPrevIndex()"
               [class.next]="$index === getNextIndex()">
            <div class="carousel-card">
              <!-- Barre de titre style Apple -->
              <div class="apple-titlebar">
                <div class="window-controls">
                  <div class="window-button close-button"></div>
                  <div class="window-button minimize-button"></div>
                  <div class="window-button maximize-button"></div>
                </div>
                <!-- Titre cliquable qui redirige vers le site -->
                <div class="window-title" (click)="visitSite($event, project.url)">{{ project.title }}</div>
              </div>

              <!-- Contenu de la fenêtre -->
              <div class="window-content">
                <img [src]="project.imageUrl" [alt]="project.title">

                <!-- Bouton de visite en haut à droite -->
                <button class="visit-corner-button" (click)="visitSite($event, project.url)" aria-label="Visiter le site">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                    <path fill-rule="evenodd" d="M8.636 3.5a.5.5 0 0 0-.5-.5H1.5A1.5 1.5 0 0 0 0 4.5v10A1.5 1.5 0 0 0 1.5 16h10a1.5 1.5 0 0 0 1.5-1.5V7.864a.5.5 0 0 0-1 0V14.5a.5.5 0 0 1-.5.5h-10a.5.5 0 0 1-.5-.5v-10a.5.5 0 0 1 .5-.5h6.636a.5.5 0 0 0 .5-.5z"/>
                    <path fill-rule="evenodd" d="M16 .5a.5.5 0 0 0-.5-.5h-5a.5.5 0 0 0 0 1h3.793L6.146 9.146a.5.5 0 1 0 .708.708L15 1.707V5.5a.5.5 0 0 0 1 0v-5z"/>
                  </svg>
                </button>
              </div>
            </div>
          </div>
        }
      </div>

      <button (click)="nextProject()" class="absolute right-8 top-1/2 -translate-y-1/2 z-30 bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm rounded-full p-4 shadow-lg hover:bg-white dark:hover:bg-gray-800 focus:outline-none transition-all duration-300">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-[#0090EC]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
        </svg>
      </button>
    </div>

    <!-- Dots navigation -->
    <div class="flex justify-center mt-6 space-x-3">
      @for (project of projects; track $index) {
        <button (click)="goToProject($index)" class="w-2.5 h-2.5 rounded-full transition-all duration-300"
                [class.bg-[#0090EC]]="currentIndex === $index"
                [class.w-4]="currentIndex === $index"
                [class.bg-gray-300]="currentIndex !== $index"
                [class.dark:bg-gray-600]="currentIndex !== $index"></button>
      }
    </div>
  </div>
</section>
