{"name": "lunatem", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "serve:ssr:lunatem": "node dist/lunatem/server/server.mjs"}, "private": true, "dependencies": {"@angular/animations": "^19.1.0", "@angular/common": "^19.1.0", "@angular/compiler": "^19.1.0", "@angular/core": "^19.1.0", "@angular/forms": "^19.1.0", "@angular/platform-browser": "^19.1.0", "@angular/platform-browser-dynamic": "^19.1.0", "@angular/platform-server": "^19.1.0", "@angular/router": "^19.1.0", "@angular/ssr": "^19.1.5", "@netlify/angular-runtime": "^2.2.0", "express": "^4.18.2", "ng-zorro-antd": "^19.2.2", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.15.0", "@emailjs/browser": "^3.11.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.1.5", "@angular/cli": "^19.1.5", "@angular/compiler-cli": "^19.1.0", "@types/express": "^4.17.17", "@types/jasmine": "~5.1.0", "@types/node": "^18.18.0", "jasmine-core": "~5.5.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "tailwindcss": "^3.4.17", "typescript": "~5.7.2"}}